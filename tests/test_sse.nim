## Comprehensive tests for Server-Sent Events (SSE) implementation in Mummy

import std/[httpclient, json, strutils, times, options, os, atomics, sequtils]
import mummy, mummy/routers, mummy/internal

# Test SSE event formatting
block:
  echo "Testing SSE event formatting..."
  
  # Test basic event with data only
  let event1 = SSEEvent(data: "Hello World")
  let formatted1 = formatSSEEvent(event1)
  let expected1 = "data: Hello World\n\n"
  doAssert formatted1 == expected1, "Basic event formatting failed"
  
  # Test event with all fields
  let event2 = SSEEvent(
    event: some("update"),
    data: "Test data",
    id: some("123"),
    retry: some(5000)
  )
  let formatted2 = formatSSEEvent(event2)
  let expected2 = "event: update\nid: 123\nretry: 5000\ndata: Test data\n\n"
  doAssert formatted2 == expected2, "Full event formatting failed"
  
  # Test multiline data
  let event3 = SSEEvent(data: "Line 1\nLine 2\nLine 3")
  let formatted3 = formatSSEEvent(event3)
  let expected3 = "data: Line 1\ndata: Line 2\ndata: Line 3\n\n"
  doAssert formatted3 == expected3, "Multiline data formatting failed"
  
  # Test empty data
  let event4 = SSEEvent(data: "")
  let formatted4 = formatSSEEvent(event4)
  let expected4 = "data: \n\n"
  doAssert formatted4 == expected4, "Empty data formatting failed"
  
  echo "✓ SSE event formatting tests passed"

# Test SSE server functionality
block:
  echo "Testing SSE server functionality..."
  
  var receivedEvents: seq[string] = @[]
  var connectionEstablished = false
  var sseConnection: SSEConnection
  
  proc handleSSE(request: Request) {.gcsafe.} =
    echo "SSE connection established"
    connectionEstablished = true
    sseConnection = request.respondSSE()
    
    # Send initial event
    sseConnection.send(SSEEvent(
      event: some("connected"),
      data: """{"message": "Connection established"}""",
      id: some("init")
    ))
  
  proc handleRoot(request: Request) {.gcsafe.} =
    let html = """
<!DOCTYPE html>
<html>
<head><title>SSE Test</title></head>
<body>
<div id="messages"></div>
<script>
const eventSource = new EventSource('/events');
eventSource.onmessage = function(event) {
  console.log('Message:', event.data);
};
eventSource.addEventListener('connected', function(event) {
  console.log('Connected:', event.data);
});
eventSource.onerror = function(event) {
  console.log('Error:', event);
};
</script>
</body>
</html>"""
    request.respond(200, @[("Content-Type", "text/html")], html)
  
  proc handleSendEvent(request: Request) {.gcsafe.} =
    if connectionEstablished and sseConnection.active:
      sseConnection.send(SSEEvent(
        event: some("test"),
        data: """{"message": "Test event", "timestamp": """ & $now() & """}""",
        id: some("test-" & $now().toTime().toUnix())
      ))
      request.respond(200, @[("Content-Type", "application/json")], """{"status": "sent"}""")
    else:
      request.respond(400, @[("Content-Type", "application/json")], """{"error": "No active SSE connection"}""")
  
  var router = Router()
  router.get("/", handleRoot)
  router.get("/events", handleSSE)
  router.post("/send", handleSendEvent)
  
  let server = newServer(router)
  
  # Start server in background
  var serverThread: Thread[void]
  proc serverProc() =
    {.gcsafe.}:
      server.serve(Port(8081))
  
  createThread(serverThread, serverProc)
  server.waitUntilReady()
  
  # Test SSE connection establishment
  block:
    echo "Testing SSE connection establishment..."
    let client = newHttpClient()
    client.timeout = 1000 # 1 second timeout
    
    try:
      # This should establish the SSE connection
      let response = client.get("http://localhost:8081/events")
      doAssert response.status.startsWith("200"), "SSE endpoint should return 200"
      doAssert response.headers.getOrDefault("Content-Type") == "text/event-stream", "Should have correct content type"
      doAssert response.headers.getOrDefault("Cache-Control") == "no-cache", "Should have no-cache header"
      doAssert response.headers.getOrDefault("Connection") == "keep-alive", "Should keep connection alive"
      
      # Give some time for connection to establish
      sleep(100)
      doAssert connectionEstablished, "SSE connection should be established"
      
    except:
      echo "Error testing SSE connection: ", getCurrentExceptionMsg()
    finally:
      client.close()
  
  # Test sending events
  block:
    echo "Testing SSE event sending..."
    let client = newHttpClient()
    
    try:
      # Send a test event
      let response = client.post("http://localhost:8081/send", "")
      doAssert response.status.startsWith("200"), "Send event should succeed"
      
      let jsonResponse = parseJson(response.body)
      doAssert jsonResponse["status"].getStr() == "sent", "Should confirm event was sent"
      
    except:
      echo "Error testing event sending: ", getCurrentExceptionMsg()
    finally:
      client.close()
  
  echo "✓ SSE server functionality tests passed"
  
  # Clean up
  server.close()

# Test SSE connection management and error handling
block:
  echo "Testing SSE connection management..."

  # Simple test without global state to avoid GC safety issues
  var connectionCount = 0

  proc handleMultiSSE(request: Request) {.gcsafe.} =
    let connection = request.respondSSE()
    inc connectionCount

    # Send welcome message
    connection.send(SSEEvent(
      event: some("welcome"),
      data: """{"client_id": """ & $connectionCount & """}""",
      id: some("welcome-" & $connectionCount)
    ))

  proc handleBroadcast(request: Request) {.gcsafe.} =
    let message = if request.body.len > 0: request.body else: "Default broadcast message"

    # For this test, just acknowledge the broadcast
    request.respond(200, @[("Content-Type", "application/json")],
                   """{"message": "Broadcast received: """ & escapeJson(message) & """, "connections": """ & $connectionCount & """}""")

  proc handleStatus(request: Request) {.gcsafe.} =
    request.respond(200, @[("Content-Type", "application/json")],
                   """{"total_connections": """ & $connectionCount & """}""")

  var router2 = Router()
  router2.get("/events", handleMultiSSE)
  router2.post("/broadcast", handleBroadcast)
  router2.get("/status", handleStatus)

  let server2 = newServer(router2)

  # Start server in background
  var serverThread2: Thread[void]
  proc serverProc2() =
    {.gcsafe.}:
      server2.serve(Port(8082))

  createThread(serverThread2, serverProc2)
  server2.waitUntilReady()

  # Test multiple connections
  block:
    echo "Testing multiple SSE connections..."
    var clients: seq[HttpClient] = @[]

    try:
      # Create multiple SSE connections
      for i in 0 ..< 3:
        let client = newHttpClient()
        client.timeout = 1000
        clients.add(client)

        # Start SSE connection (this will block, so we need to handle it differently in real tests)
        # For now, just test the endpoint responds correctly
        try:
          discard client.get("http://localhost:8082/events")
        except:
          # Expected to timeout since SSE keeps connection open
          discard

      sleep(200) # Give time for connections to establish

      # Test broadcasting
      let broadcastClient = newHttpClient()
      let response = broadcastClient.post("http://localhost:8082/broadcast", "Test broadcast message")
      doAssert response.status.startsWith("200"), "Broadcast should succeed"

      let jsonResponse = parseJson(response.body)
      echo "Broadcast result: ", jsonResponse
      doAssert jsonResponse.hasKey("message"), "Response should have message field"

      # Test status endpoint
      let statusResponse = broadcastClient.get("http://localhost:8082/status")
      doAssert statusResponse.status.startsWith("200"), "Status should succeed"
      let statusJson = parseJson(statusResponse.body)
      echo "Status result: ", statusJson

      broadcastClient.close()

    finally:
      for client in clients:
        client.close()

  echo "✓ SSE connection management tests passed"

  # Clean up
  server2.close()

# Test error conditions
block:
  echo "Testing SSE error conditions..."

  proc handleDoubleResponse(request: Request) {.gcsafe.} =
    # First response
    request.respond(200, @[("Content-Type", "text/plain")], "First response")

    # Try to start SSE after already responding - should fail
    try:
      discard request.respondSSE()
      doAssert false, "Should not be able to start SSE after responding"
    except MummyError:
      # Expected error
      discard

  proc handleInvalidSSE(request: Request) {.gcsafe.} =
    var connection = request.respondSSE()

    # Test sending to inactive connection
    connection.close()
    connection.send(SSEEvent(data: "This should be ignored"))

    # Connection should be inactive now
    doAssert not connection.active, "Connection should be inactive after close"

  var router3 = Router()
  router3.get("/double", handleDoubleResponse)
  router3.get("/invalid", handleInvalidSSE)

  let server3 = newServer(router3)

  # Start server in background
  var serverThread3: Thread[void]
  proc serverProc3() =
    {.gcsafe.}:
      server3.serve(Port(8083))

  createThread(serverThread3, serverProc3)
  server3.waitUntilReady()

  # Test double response error
  block:
    let client = newHttpClient()
    try:
      let response = client.get("http://localhost:8083/double")
      doAssert response.status.startsWith("200"), "Should get first response"
      doAssert response.body == "First response", "Should get correct body"
    finally:
      client.close()

  # Test invalid SSE operations
  block:
    let client = newHttpClient()
    try:
      discard client.get("http://localhost:8083/invalid")
    except:
      # May timeout or close connection, both are acceptable
      discard
    finally:
      client.close()

  echo "✓ SSE error condition tests passed"

  # Clean up
  server3.close()

echo "All SSE tests completed successfully!"
