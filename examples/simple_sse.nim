## Minimal Server-Sent Events (SSE) example for Mummy
## This demonstrates basic SSE capabilities

import ../src/mummy, ../src/mummy/routers
import std/[json, times, strutils, options]

proc handleSSE(request: Request) {.gcsafe.} =
  echo "Starting SSE connection for: ", request.remoteAddress
  
  let connection = request.respondSSE()
  
  # Send initial welcome event
  connection.send(SSEEvent(
    event: some("welcome"),
    data: """{"message": "SSE connection established"}""",
    id: some("welcome-1")
  ))
  
  # For demo purposes, send a few events with delay
  # In a real application, events would be triggered by business logic
  connection.send(SSEEvent(
    data: """{"timestamp": """ & $now() & """, "counter": 1}""",
    event: some("update")
  ))

proc handleRoot(request: Request) {.gcsafe.} =
  let html = """
<!DOCTYPE html>
<html>
<head>
    <title>Simple SSE Test</title>
</head>
<body>
    <h1>Simple SSE Test</h1>
    <div id="messages"></div>
    <script>
        const eventSource = new EventSource('/events');
        const messagesDiv = document.getElementById('messages');
        
        eventSource.onmessage = function(event) {
            const div = document.createElement('div');
            div.textContent = new Date().toLocaleTimeString() + ': ' + event.data;
            messagesDiv.appendChild(div);
        };
        
        eventSource.addEventListener('welcome', function(event) {
            const div = document.createElement('div');
            div.style.color = 'green';
            div.textContent = 'Welcome: ' + event.data;
            messagesDiv.appendChild(div);
        });
        
        eventSource.addEventListener('update', function(event) {
            const div = document.createElement('div');
            div.style.color = 'blue';
            div.textContent = 'Update: ' + event.data;
            messagesDiv.appendChild(div);
        });
    </script>
</body>
</html>
"""
  request.respond(200, @[("Content-Type", "text/html")], html)

proc main() =
  var router = Router()
  router.get("/", handleRoot)
  router.get("/events", handleSSE)
  
  let server = newServer(router)
  echo "Simple SSE server at http://localhost:8080"
  server.serve(Port(8080))

when isMainModule:
  main()